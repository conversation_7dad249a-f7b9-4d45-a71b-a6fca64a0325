apiVersion: v1
kind: ServiceAccount
metadata:
  name: abun-drf-service-account
  namespace: abun-prod

---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: abun-artgen-service-account
  namespace: abun-prod

---

apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: abun-drf-role
  namespace: abun-prod
rules:
  - apiGroups:
        - ""
        - apps
        - autoscaling
        - batch
        - extensions
        - policy
        - rbac.authorization.k8s.io
    resources:
      - pods
      - componentstatuses
      - configmaps
      - daemonsets
      - deployments
      - events
      - endpoints
      - horizontalpodautoscalers
      - ingress
      - jobs
      - limitranges
      - namespaces
      - nodes
      - pods
      - persistentvolumes
      - persistentvolumeclaims
      - resourcequotas
      - replicasets
      - replicationcontrollers
      - serviceaccounts
      - services
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: abun-artgen-clusterrole
  namespace: abun-prod
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - pods
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: abun-drf-service-account-clusterrole
  namespace: abun-prod
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - pods
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: abun-drf-rolebinding
  namespace: abun-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: abun-drf-role
subjects:
- namespace: abun-prod
  kind: ServiceAccount
  name: abun-drf-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: abun-artgen-rolebinding
  namespace: abun-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: abun-drf-role
subjects:
- namespace: abun-prod
  kind: ServiceAccount
  name: abun-artgen-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: abun-artgen-clusterrolebinding
  namespace: abun-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: abun-artgen-clusterrole
subjects:
- namespace: abun-prod
  kind: ServiceAccount
  name: abun-artgen-service-account

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: abun-drf-service-account-clusterrolebinding
  namespace: abun-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: abun-drf-service-account-clusterrole
subjects:
- namespace: abun-prod
  kind: ServiceAccount
  name: abun-drf-service-account

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-drf-deployment
  namespace: abun-prod
  labels:
    app: abun-drf
spec:
  replicas: 2
  selector:
    matchLabels:
      app: abun-drf
  template:
    metadata:
      labels:
        app: abun-drf
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-drf-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.81.0
        imagePullPolicy: Always
        tty: true
        stdin: true
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: chromadb-store-volume-data
          mountPath: abun_chroma_db_data
      volumes:
      - name: chromadb-store-volume-data
        persistentVolumeClaim:
          claimName: chromadb-store-volume-data-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-content-automation-deployment
  namespace: abun-prod
  labels:
    app: abun-content-automation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-content-automation
  template:
    metadata:
      labels:
        app: abun-content-automation
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-content-automation-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.81.0
        command: ["python", "manage.py", "content_automation"]
        imagePullPolicy: Always
        tty: true
        stdin: true
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-store-volume-data
          mountPath: abun_chroma_db_data
      volumes:
      - name: chromadb-store-volume-data
        persistentVolumeClaim:
          claimName: chromadb-store-volume-data-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-website-scanning-queue-processor-deployment
  namespace: abun-prod
  labels:
    app: abun-website-scanning-queue-processor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-website-scanning-queue-processor
  template:
    metadata:
      labels:
        app: abun-website-scanning-queue-processor
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-website-scanning-queue-processor-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.81.0
        command: ["python", "manage.py", "process_website_scanning_queue"]
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-store-volume-data
          mountPath: abun_chroma_db_data
      volumes:
      - name: chromadb-store-volume-data
        persistentVolumeClaim:
          claimName: chromadb-store-volume-data-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-article-internal-link-queue-processor-deployment
  namespace: abun-prod
  labels:
    app: abun-article-internal-link-queue-processor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-article-internal-link-queue-processor
  template:
    metadata:
      labels:
        app: abun-article-internal-link-queue-processor
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-article-internal-link-queue-processor-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.81.0
        command: ["python", "manage.py", "process_article_internal_link_queue"]
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-store-volume-data
          mountPath: abun_chroma_db_data
      volumes:
      - name: chromadb-store-volume-data
        persistentVolumeClaim:
          claimName: chromadb-store-volume-data-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-celery-deployment
  namespace: abun-prod
  labels:
    app: abun-celery
spec:
  replicas: 2
  selector:
    matchLabels:
      app: abun-celery
  template:
    metadata:
      labels:
        app: abun-celery
    spec:
      serviceAccountName: abun-drf-service-account
      containers:
      - name: abun-celery-container
        image: registry.gitlab.com/aminmemon/abundrfbackend/abun-api-server:v3.81.0
        command: ["celery", "-A", "AbunDRFBackend", "worker", "-l", "INFO"]
        imagePullPolicy: Always
        tty: true
        stdin: true
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        volumeMounts:
        - name: chromadb-store-volume-data
          mountPath: abun_chroma_db_data
      volumes:
      - name: chromadb-store-volume-data
        persistentVolumeClaim:
          claimName: chromadb-store-volume-data-claim
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-react-deployment
  namespace: abun-prod
  labels:
    app: abun-react
spec:
  replicas: 2
  selector:
    matchLabels:
      app: abun-react
  template:
    metadata:
      labels:
        app: abun-react
    spec:
      containers:
      - name: abun-react-container
        image: registry.gitlab.com/aminmemon/abun-react-frontend/abun-react-frontend-prod:v3.81.0
        tty: true
        stdin: true
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 3000
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: abun-admin-deployment
  namespace: abun-prod
  labels:
    app: abun-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: abun-admin
  template:
    metadata:
      labels:
        app: abun-admin
    spec:
      containers:
      - name: abun-admin-container
        image: registry.gitlab.com/aminmemon/abun-admin-react/abun-admin-prod:v2.40.0
        tty: true
        stdin: true
        imagePullPolicy: Always
        envFrom:
          - secretRef:
              name: abun-app-env-variables
        ports:
        - containerPort: 3001
      imagePullSecrets:
       - name: gitlab-registry-credentials

---

apiVersion: v1
kind: Service
metadata:
  name: abun-react-service
  namespace: abun-prod
spec:
  ports:
    - name: http
      port: 3000
      targetPort: 3000
  selector:
    app: abun-react

---

apiVersion: v1
kind: Service
metadata:
  name: abun-drf-service
  namespace: abun-prod
spec:
  ports:
    - name: http
      port: 8000
      targetPort: 8000
  selector:
    app: abun-drf

---

apiVersion: v1
kind: Service
metadata:
  name: abun-admin-service
  namespace: abun-prod
spec:
  ports:
    - name: http
      port: 3001
      targetPort: 3001
  selector:
    app: abun-admin
